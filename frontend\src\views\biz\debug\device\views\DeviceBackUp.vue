<template>
  <div class="table-box">
    <ProTable
      ref="proTable"
      :columns="columns"
      :pagination="false"
      :init-param="initParam"
      :request-auto="false"
      :data="tableData"
      table-key="deviceBackup"
      highlight-current-row
      @search="getBackupInfo"
      row-key="type"
      @selection-change="onSelectionChange"
    >
      <template #tableHeader="scope">
        <div class="flex flex-wrap gap-4 items-center header">
          <el-text class="mx-1">{{ t("device.backup.savePath") }}：</el-text>
          <el-input v-model="filePath" :placeholder="t('device.backup.setPath')" style="width: 200px" readonly> </el-input>
          <el-button type="primary" plain @click="openFileDialog" :icon="Setting" :title="t('device.backup.setPathTitle')"></el-button>
          <el-button type="success" :icon="FolderOpened" :disabled="!filePath" @click="locateFolder">
            {{ t("device.backup.locateFolder") }}
          </el-button>
          <el-button
            type="primary"
            :icon="Upload"
            :disabled="backuping || scope.selectedList.length == 0"
            @click="backupFile(tableData.filter(row => scope.selectedList.some(sel => sel.type === row.type)))"
          >
            {{ t("device.backup.startBackup") }}
          </el-button>
          <!-- <el-button type="warning" :icon="Dish" :disabled="!backuping || scope.selectedList.length === 0" plain @click="cancelBackup">
            {{ t("device.backup.cancelBackup") }}
          </el-button> -->
        </div>
      </template>
      <template #operation="scope">
        <el-button type="primary" link :icon="Briefcase" @click="backupFile(scope.row)">{{ t("device.backup.backup") }}</el-button>
      </template>
      <template #expand="scope">
        {{ scope.row }}
      </template>
    </ProTable>
  </div>
</template>

<script setup lang="tsx" name="useProTable">
import { ref, reactive, onMounted, onBeforeUnmount, computed } from "vue";
import { useI18n } from "vue-i18n";
import ProTable from "@/components/ProTable/index.vue";
import { ProTableInstance, ColumnProps } from "@/components/ProTable/interface";
import { useDebugStore } from "@/stores/modules/debug";
import { osControlApi } from "@/api/modules/biz/os";
import { ElMessage } from "element-plus";
import dayjs from "dayjs";
import { Upload, Setting, Briefcase, FolderOpened } from "@element-plus/icons-vue";
import { BackupInfo } from "@/api/interface/biz/debug/backupinfo";
// 扩展类型，解决 hasTask、taskId 语法错误
import { v4 as uuidv4 } from "uuid"; // 需安装 uuid 库

// 备份类型常量，避免使用中文
const BackupType = {
  ParamValue: "paramValue",
  FaultInfo: "faultInfo",
  CidConfigPrjLog: "cidConfigPrjLog",
  WaveReport: "waveReport"
} as const;
type BackupTypeEnum = (typeof BackupType)[keyof typeof BackupType];

type BackupTableRow = BackupInfo & {
  hasTask?: boolean;
  taskId?: string;
  progress: number;
  status: string;
  type: BackupTypeEnum;
  percentType?: string; // 新增
};

// 新增：后端一键备份API（假设已实现）
import { backupApi } from "@/api/modules/biz/debug/backup";

const { debugIndex } = useDebugStore();
// 透传装置ID
const props = defineProps<{ deviceId: string }>();

const { t } = useI18n();

// ProTable 实例
const proTable = ref<ProTableInstance>();
// 如果表格需要初始化请求参数，直接定义传给 ProTable (之后每次请求都会自动带上该参数，此参数更改之后也会一直带上，改变此参数会自动刷新表格数据)
const initParam = reactive({ type: 1 });
const { addConsole } = useDebugStore();
const backuping = ref(false);
// const canceling = ref(false);
// tableData 类型修正
const tableData = ref<BackupTableRow[]>([]);
const filePath = ref("");
import { ipc } from "@/api/request/ipcRenderer";

// 使用设备ID作为键的一部分，确保每个设备的路径独立保存
const FILE_PATH_KEY = computed(() => `device_backup_filePath_${props.deviceId}`);
const SELECTED_TYPE_KEY = computed(() => `device_backup_selectedTypes_${props.deviceId}`);

// 新增：单行任务状态
function initTableData() {
  tableData.value = [
    {
      type: BackupType.ParamValue,
      progress: 0,
      status: "",
      hasTask: false,
      taskId: "",
      percentType: ""
    },
    {
      type: BackupType.FaultInfo,
      progress: 0,
      status: "",
      hasTask: false,
      taskId: "",
      percentType: ""
    },
    {
      type: BackupType.CidConfigPrjLog,
      progress: 0,
      status: "",
      hasTask: false,
      taskId: "",
      percentType: ""
    },
    {
      type: BackupType.WaveReport,
      progress: 0,
      status: "",
      hasTask: false,
      taskId: "",
      percentType: ""
    }
  ];
}
// 勾选变化时保存 type
function onSelectionChange(selectedRows: BackupTableRow[]) {
  const types = selectedRows.map(row => row.type);
  localStorage.setItem(SELECTED_TYPE_KEY.value, JSON.stringify(types));
}
const getBackupInfo = async () => {
  // 1. 记录当前已勾选的 type
  let checkedTypes: string[] = [];
  try {
    const saved = localStorage.getItem(SELECTED_TYPE_KEY.value);
    if (saved) {
      checkedTypes = JSON.parse(saved);
    } else {
      checkedTypes = proTable.value?.selectedList?.map(row => row.type) || [];
    }
  } catch {
    checkedTypes = proTable.value?.selectedList?.map(row => row.type) || [];
  }
  initTableData();
  proTable.value?.refresh();
  // 2. 刷新后恢复勾选
  setTimeout(() => {
    if (proTable.value && checkedTypes.length > 0) {
      tableData.value.forEach(row => {
        if (checkedTypes.includes(row.type)) {
          proTable.value?.element?.toggleRowSelection(row, true);
        }
      });
    }
  }, 0);
};
// backupFile 传递给后端的 types
const backupFile = async (selectedList: BackupTableRow[]) => {
  if (!filePath.value) {
    ElMessage.warning(t("device.backup.setPath"));
    addConsole(t("device.backup.console.pathNotSet"));
    return;
  }
  if (!selectedList || selectedList.length === 0) {
    ElMessage.warning(t("device.backup.noTypeSelected"));
    addConsole(t("device.backup.console.noTypeSelected"));
    return;
  }
  backuping.value = true;
  addConsole(t("device.backup.console.startBackup", { types: selectedList.map(i => i.type).join(", "), path: filePath.value }));
  try {
    const now = dayjs();
    const backupRoot = `${filePath.value}/back-${now.format("YYYYMMDD-HHmmss")}`;
    const taskId = uuidv4(); // 生成uuid
    selectedList.forEach(row => (row.taskId = taskId)); // 保存到每个row
    // 组装备份类型，直接传英文枚举值
    const types = selectedList.map(item => item.type);
    const res = await backupApi.oneKeyBackupByDevice(props.deviceId, {
      backupRoot,
      types,
      taskId // 传递给后端
    });
    if (res && res.code === 0) {
      ElMessage.success(t("device.backup.backupSuccess"));
      // addConsole("备份成功");
    } else {
      ElMessage.error(t("device.backup.backupFailed") + (res?.msg || ""));
      // addConsole("备份失败：" + (res?.msg || "未知错误"));
    }
  } catch (err: any) {
    ElMessage.error(t("device.backup.backupFailed") + (err.message || err));
    addConsole(t("device.backup.console.backupException", { error: err.message || err }));
  } finally {
    backuping.value = false;
  }
};
const openFileDialog = async () => {
  const savePath = await osControlApi.selectFolder();
  if (String(savePath) !== "") {
    filePath.value = String(savePath);
    localStorage.setItem(FILE_PATH_KEY.value, filePath.value); // 记忆路径
    addConsole(t("device.backup.console.pathSelected", { path: filePath.value }));
  } else {
    addConsole(t("device.backup.console.pathNotSelected"));
  }
};
const locateFolder = async () => {
  if (!filePath.value) {
    addConsole(t("device.backup.console.pathNotSetForLocate"));
    return;
  }
  try {
    await osControlApi.openDirectory({ id: filePath.value });
    addConsole(t("device.backup.console.folderOpened", { path: filePath.value }));
  } catch (err: any) {
    ElMessage.error(t("device.backup.openFolderFailed") + (err.message || err));
    addConsole(t("device.backup.console.openFolderFailed", { error: err.message || err }));
  }
};
// const cancelBackup = async () => {
//   canceling.value = true;
//   addConsole("尝试取消备份任务");
//   try {
//     // 找到有taskId的row
//     const runningTask = tableData.value.find(row => row.taskId);
//     if (!runningTask?.taskId) {
//       ElMessage.error("未找到任务ID，无法取消");
//       addConsole("未找到任务ID，无法取消备份");
//       return;
//     }
//     await backupApi.cancelBackup({ id: runningTask.taskId });
//     ElMessage.success(t("device.backup.cancelSuccess"));
//     addConsole("备份任务已取消");
//   } catch (err: any) {
//     // 修正类型
//     ElMessage.error(t("device.backup.cancelFailed") + (err.message || err));
//     addConsole("取消备份失败：" + (err.message || err));
//   } finally {
//     canceling.value = false;
//     backuping.value = false;
//     // 可重置进度条、状态等
//   }
// };

// 监听备份进度通知
function handleBackupNotify(_event: any, notify: any) {
  console.log("notify", notify);
  if (!notify || notify.type === undefined || !notify.data) return;
  // 多装置过滤：仅处理当前组件对应的 deviceId 事件
  if (notify.deviceId && notify.deviceId !== props.deviceId) return;

  // 新增：处理 type 为 "backup" 的情况
  if (notify.type === "backup") {
    addConsole(t("device.backup.console.taskCompleted"));
    return;
  }
  if (notify.type === "cancelbackup") {
    addConsole(t("device.backup.console.taskCancelled"));
    return;
  }
  // type: 具体备份类型，data: { step, percent, status, errorMsg }
  const { type, data } = notify;
  // 找到 tableData 中对应类型的行
  const row = tableData.value.find(item => item.type === type);
  if (row) {
    row.progress = data.percent;
    row.status = data.status || data.step || "";
    // 状态赋值
    if (data.errorMsg) {
      row.status = `${data.errorMsg}`;
      row.percentType = "exception";
      addConsole(t("device.backup.console.typeError", { type, error: data.errorMsg }));
    } else if (data.status === "completed" || data.status === "success" || data.percent === 100) {
      row.percentType = "success";
      addConsole(t("device.backup.console.typeCompleted", { type }));
    } else if (data.status === "cancelled" || data.status === "canceled" || data.status === "USER_CANCEL") {
      row.percentType = "warning";
      row.status = t("device.backup.backupStatus.userCancelled");
      addConsole(t("device.backup.console.typeCancelled", { type }));
    } else if (data.status === "failed" || data.status === "error") {
      row.percentType = "exception";
      addConsole(t("device.backup.console.typeFailed", { type }));
    } else if (data.status === "DATA_TRANSFER") {
      row.percentType = "primary";
      row.status = t("device.backup.backupStatus.transferring");
    } else {
      row.percentType = "";
      // addConsole(`类型[${type}]进度：${data.percent}%，状态：${data.status || data.step || "-"}`);
    }
    // 强制刷新tableData，确保进度条刷新
    tableData.value = [...tableData.value];
  }
}

onMounted(() => {
  const savedPath = localStorage.getItem(FILE_PATH_KEY.value);
  if (savedPath) {
    filePath.value = savedPath;
    console.log(`DeviceBackUp mounted: 恢复保存的路径 ${savedPath} for device ${props.deviceId}`);
  } else {
    console.log(`DeviceBackUp mounted: 没有找到保存的路径 for device ${props.deviceId}`);
  }
  getBackupInfo();
  if (ipc && ipc.on) {
    ipc.on("backup_notify", handleBackupNotify);
  }
});

onBeforeUnmount(() => {
  if (ipc && ipc.removeListener) {
    ipc.removeListener("backup_notify", handleBackupNotify);
  }
});

// 表格配置项
const columns = reactive<ColumnProps<BackupTableRow>[]>([
  { type: "selection", prop: "checked", fixed: "left", width: 70 },
  { type: "index", label: t("device.backup.sequence"), fixed: "left", width: 60 },
  {
    prop: "type",
    label: t("device.backup.backupType"),
    width: 200,
    render: scope => {
      let label = "";
      switch (scope.row.type) {
        case BackupType.ParamValue:
          label = t("device.backup.backupTypes.paramValue");
          break;
        case BackupType.FaultInfo:
          label = t("device.backup.backupTypes.faultInfo");
          break;
        case BackupType.CidConfigPrjLog:
          label = t("device.backup.backupTypes.cidConfigPrjLog");
          break;
        case BackupType.WaveReport:
          label = t("device.backup.backupTypes.waveReport");
          break;
        default:
          label = scope.row.type;
      }
      return <span>{label}</span>;
    }
  },
  {
    prop: "desc",
    label: t("device.backup.backupDesc"),
    minWidth: 300,
    render: scope => {
      let desc = "";
      switch (scope.row.type) {
        case BackupType.ParamValue:
          desc = t("device.backup.backupDescTypes.paramValue");
          break;
        case BackupType.FaultInfo:
          desc = t("device.backup.backupDescTypes.faultInfo");
          break;
        case BackupType.CidConfigPrjLog:
          desc = t("device.backup.backupDescTypes.cidConfigPrjLog");
          break;
        case BackupType.WaveReport:
          desc = t("device.backup.backupDescTypes.waveReport");
          break;
        default:
          desc = "-";
      }
      return <span>{desc}</span>;
    }
  },
  {
    prop: "progress",
    label: t("device.backup.progress"),
    render: scope => {
      return (
        <div>
          <el-progress percentage={scope.row.progress} text-inside={true} stroke-width={16} status={scope.row.percentType}></el-progress>
        </div>
      );
    }
  },
  {
    prop: "status",
    label: t("device.backup.status")
  }
]);

watch(
  debugIndex.compData,
  newValue => {
    console.log("newValue:", newValue);
    if (newValue) {
      proTable.value?.reset();
      getBackupInfo();
    }
  },
  { deep: true }
);
</script>

<style lang="css" scoped>
.table-box {
  display: flex;
  flex: 1;
  flex-direction: column;
  width: 100%;
}
.header {
  margin-bottom: 8px;
}
</style>
